# Acqwired

We help PE/VC companies to invest better with help of AI and ranking companies.

## Code Formatting (Visual Studio Code)

- Please make sure you are using [Prettier Extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
- Please make sure you have _Editor: Format On Save_ enabled in your VS Code Settings. You can press ctrl+, to open and find this setting there.

### Landing Page - https://acqwired.com/

Infrastructure:

---

### Installation

##### Dependencies

- Docker
- Docker-compose
- Minikube
- Kubectl
- Skaffold

You can use `make setup-dependencies` to install dependencies on `apt` based Linux systems with.

You need to add some github configuration to .ssh/config:

```
Host github.com
  Preferredauthentications publickey
  IdentityFile ~/.ssh/id_rsa
```

Details: https://sanderknape.com/2019/06/installing-private-git-repositories-npm-install-docker/

After installing required dependencies;

- Clone the repository
- Run `make install` to install application dependencies
- Run `make start` to run build & run the application
- Run `make logs` to assign all the container logs to current shell session
- Add `127.0.0.1 acqwired.local console.acqwired.local` entry to /etc/hosts.
- Go to https://console.acqwired.local/.

### PostgreSQL Setup:

- Access PostgreSQL Service:
- Connect to the PostgreSQL service.
- Run Migration Deployment:
- Execute the this command to deploy migrations: `npx prisma migrate deploy`
- Generate Migrations: `npx prisma generate`
- Seed Data: `npx prisma db seed`

### Clean && Re-run

- Run `make stop` to stop the application
- Run `make clean` to clear major part of the app (a.k.a. soft clean).
- Run `make hard-clean` to wipe out everything if you see any instability.
- You can run `KEEP_DB=1 make clean` to clean except db purge
- Run `make start` again to re-run it.

You can combine these make targets like `make stop clean start` in one command to stop & clean & run.

To see more help run `make help`.

---

### Development

We use nextjs, react, tailwindcss in our landing page (frontend). This is the website service, serves our public pages. We have same tech-stack for members as well, it's called `console`. Console and Frontend are the only services serves UI.

We also have 2 RestAPI (Json API), called auth and dataapi. `auth` is responsible of generating JWT tokens and authentication related actions, like login, forget password, logout, verify access token, and refresh token etc.

We have Nginx server, running as reverse proxy, routes requests to related services.

Auth API accessable only from Console's NextJS API. See infrastructure drawings to understand better.

- Clone the repository (You will see `acqwired` folder)
- Go to auth service root folder by `cd acqwired/auth`
- Run `npm install`
- Go to dataapi service root folder by `cd acqwired/dataapi`
- Run `npm install`
- We are mounting auth/node_modules and datapi/node_modules folders to docker containers, so you must execute the commands above.
- Come to project root folder by `cd acqwired` and execute `docker-compose -f docker-compose-dev.yml up --build -d`
- You should see an output similar to this:

```
docker ps -a
CONTAINER ID   IMAGE      COMMAND                  CREATED          STATUS                      PORTS                      NAMES
f366cb420891   nginx      "/docker-entrypoint.…"   29 minutes ago   Up 29 minutes               0.0.0.0:80->80/tcp         nginx
073cdbc187e5   console    "docker-entrypoint.s…"   29 minutes ago   Up 29 minutes               0.0.0.0:3003->3003/tcp     console
e448e4ea5b05   data       "docker-entrypoint.s…"   29 minutes ago   Up 29 minutes               0.0.0.0:3002->3002/tcp     data
3ae521276ae8   auth       "docker-entrypoint.s…"   29 minutes ago   Up 29 minutes               0.0.0.0:3001->3001/tcp     auth
a6be59e0142a   mongodb    "docker-entrypoint.s…"   29 minutes ago   Up 29 minutes (unhealthy)   0.0.0.0:27017->27017/tcp   mongodb
88cb17d9c08e   frontend   "docker-entrypoint.s…"   29 minutes ago   Up 29 minutes               0.0.0.0:3000->3000/tcp     frontend
```

- You can see API logs by typing `docker logs auth` or `docker logs data`.
- You can also find API logs in `logs` folder of each folder, so `ls -lah acqwired/auth/logs` or `ls -lah acqwired/dataapi/logs`, you can tail these files to monitor logs.
- You can also use `docker logs` command for any service, for instance to see `console` logs just run `docker logs console`
- If you do not have any other docker container and want to stop and clean all the related files/cache/images, run the following in project root `docker-compose down && docker system prune -a -f && docker image prune -a -f && docker volume prune -f`

---

# Deployment

We build and deploy all the services by Github Actions and deploy to GKE. See `.github/workflows` folder for details.

## GKE Provisoning

We can use following gcloud commmand to provision GKE Cluster

```
gcloud beta container --project "<PROJECT_ID>" clusters create "<Cluster Naae>" \
--no-enable-basic-auth \
--cluster-version "1.29.2-gke.1060000"\
--release-channel "None" \
--machine-type "e2-standard-4" \
--image-type "COS_CONTAINERD" \
--disk-type "pd-balanced" \
--disk-size "100" \
--metadata disable-legacy-endpoints=true \
--scopes "https://www.googleapis.com/auth/cloud-platform" \
--spot \
--num-nodes "1" \
--logging=SYSTEM,WORKLOAD \
--monitoring=SYSTEM,STORAGE,POD,CONTROLLER_MANAGER,SCHEDULER,API_SERVER,DEPLOYMENT,STATEFULSET,DAEMONSET,HPA \
--enable-private-nodes \
--enable-master-global-access \
--private-endpoint-subnetwork="projects/<PROJECT_ID>/regions/us-central1/subnetworks/tier-1" \
--enable-ip-alias \
--network "projects/<PROJECT_ID>/global/networks/dev-net" \
--subnetwork "projects/<PROJECT_ID>/regions/us-central1/subnetworks/tier-1" \
--cluster-secondary-range-name "tier-1-pods" \
--services-secondary-range-name "tier-1-services" \
--no-enable-intra-node-visibility \
--default-max-pods-per-node "110" \
--security-posture=standard \
--workload-vulnerability-scanning=standard \
--no-enable-master-authorized-networks \
--addons HorizontalPodAutoscaling,HttpLoadBalancing,GcePersistentDiskCsiDriver,GcpFilestoreCsiDriver \
--enable-autoupgrade \
--enable-autorepair \
--max-surge-upgrade 0 \
--max-unavailable-upgrade 0 \
--binauthz-evaluation-mode=DISABLED \
--enable-managed-prometheus \
--enable-vertical-pod-autoscaling \
--workload-pool "acqwired-dev.svc.id.goog" \
--enable-shielded-nodes \
--enable-l4-ilb-subsetting \
--node-locations "us-central1-a"
```

## Using Github Actions to Build and Deploy

- Currently workflows have been added to build and deploy services on GKE
- Github is using WIF to authenticate to GCP and GKE
- Maintenance is being activated using makefile before deloyment and disable aftter deployment

## Rollback version using kubectl

- I have set revisionHistoryLimit: 50 so that you can use this command to rollback
- To check history of revisions run:

```
kubectl rollout history deployment/<app name>
```

- To rollback to specific revision run:

```
kubectl rollout undo deployment/<app name> --to-revision=<revision number>
```

## Authentication to Cluster to be able to execute kubectl commands

1. Login using gcloud Cli
   ```console
   gcloud auth login
   ```

Set your project:

```console
gcloud config set project acqwired-dev
```

OR

      ```console
      gcloud config set project acqwired-prod
      ```

2. Authenticate to GKE Cluster:

   - For development environment:
     ```console
     gcloud container clusters get-credentials dev-cluster --location=us-central1-a
     ```
   - For production environment:
     ```console
     gcloud container clusters get-credentials prod-cluster --location=us-east4
     ```

3. Setup IAP Tunnel using bastion to be able to connect to GKE API

   - For development environment:
     ```console
     gcloud compute ssh gke-bastion --project acqwired-dev --zone us-central1-a --tunnel-through-iap --ssh-key-expire-after 30m -- -L 8888:127.0.0.1:8888  -N -q -f
     ```
   - For production environment:
     ```console
     gcloud compute ssh gke-bastion --project acqwired-prod --zone us-east4-a --tunnel-through-iap --ssh-key-expire-after 30m -- -L 8888:127.0.0.1:8888  -N -q -f
     ```

4. Now you can use kubectl commands setting up proxy variable
   ```console
   export HTTPS_PROXY=localhost:8888
   kubectl get pods
   ```

## Connecting to PostgreSQL instance

Make sure you have installed `cloud-sql-proxy`.

1. Ensure your local environment is not running

```
make stop
```

2. Open a terminal window and set the `DATABASE_URL` environment variable (Change the DATABASE_PASSWORD).

```
export DATABASE_URL="postgresql://acqwired:DATABASE_PASSWORD@localhost:5432/acqwired?schema=public"
```

3. Login with your browser:

```
gcloud auth application-default login
```

4. Connect on terminal 1 to dev instance:

```
~/google-cloud-sdk/cloud-sql-proxy acqwired-dev:us-central1:acqwired-dev
```

5. If CloudSQL Instance is private then use these steps:

```
gcloud compute start-iap-tunnel gke-bastion 22 \
  --zone=us-east4-a --local-host-port=localhost:4226

ssh -L 5432:localhost:5432 \
  -i ~/.ssh/google_compute_engine \
  -p 4226 localhost \
  -- /tmp/cloud_sql_proxy instances=acqwired-prod:us-east4:acqwired-prod=tcp:5432
```

## Reset or migrate database version

Note: See connecting to PostgreSQL instance above.

After you connectd, to reset database:

```
cd postgresql/ && ./node_modules/.bin/prisma migrate dev apply
```

To migrate database:

```
cd postgresql/ && ./node_modules/.bin/prisma migrate
```

# Testing

## Worker

To run all tests:

(while in worker directory)

```
npm run test
```

## DataAPI

To run dataapi tests:

```
make dataapi-test
```

This command will:

1. Reset the test database to a clean state
2. Run the dataapi tests
3. Show test execution results

The tests run in a test environment with a separate database to ensure test isolation.
